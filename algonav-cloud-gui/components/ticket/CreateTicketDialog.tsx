'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stack,
  Typography,
  Chip,
  Box,
  IconButton,
  CircularProgress
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import AddIcon from '@mui/icons-material/Add';

interface Target {
  target_type: 'job' | 'task' | 'dataset';
  target_id: number;
  name?: string; // Optional display name
}

interface CreateTicketDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (ticketData: {
    title: string;
    description?: string;
    priority: string;
    assigneeId?: string;
    targets?: Target[];
  }) => Promise<void>;
  preselectedTargets?: Target[];
  loading?: boolean;
}

const priorityOptions = [
  { value: 'low', label: 'Low' },
  { value: 'medium', label: 'Medium' },
  { value: 'high', label: 'High' },
  { value: 'urgent', label: 'Urgent' }
];

const targetTypeLabels = {
  job: 'Job',
  task: 'Task',
  dataset: 'Dataset'
};

export function CreateTicketDialog({
  open,
  onClose,
  onSubmit,
  preselectedTargets = [],
  loading = false
}: CreateTicketDialogProps) {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [priority, setPriority] = useState('medium');
  const [assigneeId, setAssigneeId] = useState('');
  const [targets, setTargets] = useState<Target[]>([]);
  const [submitting, setSubmitting] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  // Initialize targets with preselected ones
  useEffect(() => {
    if (preselectedTargets && preselectedTargets.length > 0) {
      setTargets([...preselectedTargets]);
    }
  }, [preselectedTargets]);

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (open) {
      setTitle('');
      setDescription('');
      setPriority('medium');
      setAssigneeId('');
      setTargets(preselectedTargets ? [...preselectedTargets] : []);
      setErrors({});
    }
  }, [open, preselectedTargets]);

  const validateForm = useCallback(() => {
    const newErrors: { [key: string]: string } = {};

    if (!title.trim()) {
      newErrors.title = 'Title is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [title]);

  const handleSubmit = useCallback(async () => {
    if (!validateForm()) return;

    setSubmitting(true);
    try {
      await onSubmit({
        title: title.trim(),
        description: description.trim() || undefined,
        priority,
        assigneeId: assigneeId || undefined,
        targets: targets.length > 0 ? targets : undefined
      });
      onClose();
    } catch (error) {
      console.error('Failed to create ticket:', error);
      setErrors({ submit: 'Failed to create ticket. Please try again.' });
    } finally {
      setSubmitting(false);
    }
  }, [validateForm, onSubmit, onClose, title, description, priority, assigneeId, targets]);

  const handleRemoveTarget = useCallback((index: number) => {
    setTargets(prev => prev.filter((_, i) => i !== index));
  }, []);

  const getTargetDisplayName = useCallback((target: Target) => {
    if (target.name) {
      return `${targetTypeLabels[target.target_type]} "${target.name}"`;
    }
    return `${targetTypeLabels[target.target_type]} #${target.target_id}`;
  }, []);

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          minHeight: '400px'
        }
      }}
    >
      <DialogTitle>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Typography variant="h6">Create Support Ticket</Typography>
          <IconButton onClick={onClose} disabled={submitting}>
            <CloseIcon />
          </IconButton>
        </Stack>
      </DialogTitle>

      <DialogContent>
        <Stack spacing={3} sx={{ mt: 1 }}>
          {/* Title */}
          <TextField
            label="Title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            error={!!errors.title}
            helperText={errors.title}
            fullWidth
            required
            disabled={submitting}
          />

          {/* Description */}
          <TextField
            label="Description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            multiline
            rows={4}
            fullWidth
            disabled={submitting}
            placeholder="Describe the issue or request in detail..."
          />

          {/* Priority */}
          <FormControl fullWidth disabled={submitting}>
            <InputLabel id="priority-select-label">Priority</InputLabel>
            <Select
              labelId="priority-select-label"
              value={priority}
              label="Priority"
              onChange={(e) => setPriority(e.target.value as string)}
            >
              {priorityOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {/* Assignee (placeholder for now) */}
          <TextField
            label="Assignee Email (Optional)"
            value={assigneeId}
            onChange={(e) => setAssigneeId(e.target.value)}
            fullWidth
            disabled={submitting}
            placeholder="Leave empty for auto-assignment"
            helperText="Enter the email address of the person to assign this ticket to"
          />

          {/* Related Items */}
          <Box>
            <Typography variant="subtitle2" gutterBottom>
              Related Items
            </Typography>
            {targets.length > 0 ? (
              <Stack direction="row" spacing={1} flexWrap="wrap" sx={{ gap: 1 }}>
                {targets.map((target, index) => (
                  <Chip
                    key={index}
                    label={getTargetDisplayName(target)}
                    onDelete={submitting ? undefined : () => handleRemoveTarget(index)}
                    size="small"
                    color="primary"
                    variant="outlined"
                  />
                ))}
              </Stack>
            ) : (
              <Typography variant="body2" color="text.secondary">
                No related items selected
              </Typography>
            )}
          </Box>

          {/* Error message */}
          {errors.submit && (
            <Typography color="error" variant="body2">
              {errors.submit}
            </Typography>
          )}
        </Stack>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 1 }}>
        <Button 
          onClick={onClose} 
          disabled={submitting}
          color="inherit"
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={submitting || !title.trim()}
          startIcon={submitting ? <CircularProgress size={16} /> : <AddIcon />}
        >
          {submitting ? 'Creating...' : 'Create Ticket'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
