'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  IconButton,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Chip,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  Paper,
  CircularProgress,
  Alert
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import SendIcon from '@mui/icons-material/Send';
import PersonIcon from '@mui/icons-material/Person';
import { formatDistanceToNow } from 'date-fns';

interface Ticket {
  id: string;
  title: string;
  description?: string;
  status: 'open' | 'in_progress' | 'waiting_on_customer' | 'resolved' | 'closed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  created_at: string;
  updated_at: string;
  creator: { id: string; email: string };
  assignee?: { id: string; email: string };
  ticket_targets?: Array<{ target_type: string; target_id: number }>;
  ticket_messages?: Array<{
    id: string;
    body: string;
    created_at: string;
    author: { id: string; email: string };
  }>;
  ticket_status_history?: Array<{
    id: string;
    old_status: string;
    new_status: string;
    changed_at: string;
    changed_by: { id: string; email: string };
  }>;
}

interface TicketDetailDrawerProps {
  open: boolean;
  ticket: Ticket | null;
  onClose: () => void;
  onStatusChange?: (ticketId: string, status: string) => void;
  onAddMessage?: (ticketId: string, message: string) => void;
  loading?: boolean;
}

const statusColors = {
  open: 'primary',
  in_progress: 'info',
  waiting_on_customer: 'warning',
  resolved: 'success',
  closed: 'default',
  cancelled: 'error'
} as const;

const priorityColors = {
  low: 'default',
  medium: 'primary',
  high: 'warning',
  urgent: 'error'
} as const;

const statusLabels = {
  open: 'Open',
  in_progress: 'In Progress',
  waiting_on_customer: 'Waiting on Customer',
  resolved: 'Resolved',
  closed: 'Closed',
  cancelled: 'Cancelled'
};

const priorityLabels = {
  low: 'Low',
  medium: 'Medium',
  high: 'High',
  urgent: 'Urgent'
};

export function TicketDetailDrawer({
  open,
  ticket,
  onClose,
  onStatusChange,
  onAddMessage,
  loading = false
}: TicketDetailDrawerProps) {
  const [newMessage, setNewMessage] = useState('');
  const [submittingMessage, setSubmittingMessage] = useState(false);

  const handleStatusChange = (newStatus: string) => {
    if (ticket && onStatusChange) {
      onStatusChange(ticket.id, newStatus);
    }
  };

  const handleAddMessage = async () => {
    if (!ticket || !newMessage.trim() || !onAddMessage) return;

    setSubmittingMessage(true);
    try {
      await onAddMessage(ticket.id, newMessage.trim());
      setNewMessage('');
    } catch (error) {
      console.error('Failed to add message:', error);
    } finally {
      setSubmittingMessage(false);
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
      handleAddMessage();
    }
  };

  if (!ticket) {
    return (
      <Drawer
        anchor="right"
        open={open}
        onClose={onClose}
        PaperProps={{
          sx: {
            width: { xs: '100%', sm: 600 },
            maxWidth: '100vw'
          }
        }}
      >
        <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
          <Box sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
            <Stack direction="row" alignItems="center" justifyContent="space-between">
              <Typography variant="h6" component="h2">
                Loading Ticket...
              </Typography>
              <IconButton onClick={onClose}>
                <CloseIcon />
              </IconButton>
            </Stack>
          </Box>
          <Box sx={{ flex: 1, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            <CircularProgress />
          </Box>
        </Box>
      </Drawer>
    );
  }

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          width: { xs: '100%', sm: 600 },
          maxWidth: '100vw'
        }
      }}
    >
      <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Header */}
        <Box sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
          <Stack direction="row" alignItems="center" justifyContent="space-between">
            <Typography variant="h6" component="h2">
              Ticket #{ticket.id}
            </Typography>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Stack>
        </Box>

        {/* Content */}
        <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Stack spacing={3}>
              {/* Title and Description */}
              <Box>
                <Typography variant="h5" gutterBottom>
                  {ticket.title}
                </Typography>
                {ticket.description && (
                  <Typography variant="body1" color="text.secondary">
                    {ticket.description}
                  </Typography>
                )}
              </Box>

              {/* Status and Priority */}
              <Stack direction="row" spacing={2}>
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Status
                  </Typography>
                  <FormControl size="small" sx={{ minWidth: 150 }}>
                    <Select
                      value={ticket.status}
                      onChange={(e) => handleStatusChange(e.target.value)}
                    >
                      <MenuItem value="open">Open</MenuItem>
                      <MenuItem value="in_progress">In Progress</MenuItem>
                      <MenuItem value="waiting_on_customer">Waiting on Customer</MenuItem>
                      <MenuItem value="resolved">Resolved</MenuItem>
                      <MenuItem value="closed">Closed</MenuItem>
                      <MenuItem value="cancelled">Cancelled</MenuItem>
                    </Select>
                  </FormControl>
                </Box>
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Priority
                  </Typography>
                  <Chip
                    label={priorityLabels[ticket.priority]}
                    color={priorityColors[ticket.priority]}
                    size="small"
                    variant="outlined"
                  />
                </Box>
              </Stack>

              {/* Assignee and Creator */}
              <Stack direction="row" spacing={2}>
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Assignee
                  </Typography>
                  <Stack direction="row" alignItems="center" spacing={1}>
                    <Avatar sx={{ width: 24, height: 24 }}>
                      <PersonIcon fontSize="small" />
                    </Avatar>
                    <Typography variant="body2">
                      {ticket.assignee?.email || 'Unassigned'}
                    </Typography>
                  </Stack>
                </Box>
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Creator
                  </Typography>
                  <Stack direction="row" alignItems="center" spacing={1}>
                    <Avatar sx={{ width: 24, height: 24 }}>
                      <PersonIcon fontSize="small" />
                    </Avatar>
                    <Typography variant="body2">
                      {ticket.creator?.email || 'Unknown'}
                    </Typography>
                  </Stack>
                </Box>
              </Stack>

              {/* Targets */}
              {ticket.ticket_targets && ticket.ticket_targets.length > 0 && (
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Related Items
                  </Typography>
                  <Stack direction="row" spacing={1} flexWrap="wrap">
                    {ticket.ticket_targets.map((target, index) => (
                      <Chip
                        key={index}
                        label={`${target.target_type} #${target.target_id}`}
                        size="small"
                        variant="outlined"
                      />
                    ))}
                  </Stack>
                </Box>
              )}

              <Divider />

              {/* Messages Section */}
              <Box>
                <Typography variant="h6" gutterBottom>
                  Messages
                </Typography>
                
                {/* Messages List */}
                <Stack spacing={2} sx={{ mb: 2 }}>
                  {ticket.ticket_messages?.map((message) => (
                    <Paper key={message.id} sx={{ p: 2 }}>
                      <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 1 }}>
                        <Avatar sx={{ width: 24, height: 24 }}>
                          <PersonIcon fontSize="small" />
                        </Avatar>
                        <Typography variant="subtitle2">
                          {message.author?.email || 'Unknown User'}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatDistanceToNow(new Date(message.created_at), { addSuffix: true })}
                        </Typography>
                      </Stack>
                      <Typography variant="body2">
                        {message.body}
                      </Typography>
                    </Paper>
                  ))}
                </Stack>

                {/* Add Message */}
                <Stack spacing={2}>
                  <TextField
                    multiline
                    rows={3}
                    placeholder="Add a message... (Ctrl+Enter to submit)"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyDown={handleKeyPress}
                    disabled={submittingMessage}
                  />
                  <Button
                    variant="contained"
                    startIcon={submittingMessage ? <CircularProgress size={16} /> : <SendIcon />}
                    onClick={handleAddMessage}
                    disabled={!newMessage.trim() || submittingMessage}
                    sx={{ alignSelf: 'flex-start' }}
                  >
                    Add Message
                  </Button>
                </Stack>
              </Box>
            </Stack>
          )}
        </Box>

        {/* Footer with timestamps */}
        <Box sx={{ p: 2, borderTop: '1px solid', borderColor: 'divider' }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="caption" color="text.secondary">
              Created: {new Date(ticket.created_at).toLocaleString()}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Updated: {new Date(ticket.updated_at).toLocaleString()}
            </Typography>
          </Stack>
        </Box>
      </Box>
    </Drawer>
  );
}
