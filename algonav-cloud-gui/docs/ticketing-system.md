# Ticketing System Documentation

## Overview

The AlgoNav Cloud GUI now includes a comprehensive ticketing system that allows users to create, manage, and track support requests. The system is fully integrated with the existing job, task, and dataset management features.

## Features

- **Ticket Creation**: Create support tickets with title, description, priority, and assignee
- **Target Linking**: Link tickets to specific jobs, tasks, or datasets
- **Status Management**: Track ticket progress through various states
- **Comments System**: Add comments and collaborate on tickets
- **Real-time Updates**: Automatic updates using Supabase realtime subscriptions
- **Dashboard Integration**: View ticket summaries on the main dashboard
- **Search & Filtering**: Find tickets by status, content, or other criteria

## Database Schema

### Tables

#### `tickets`
- `id` (integer, primary key)
- `creator_id` (uuid, foreign key to auth.users)
- `assignee_id` (uuid, foreign key to auth.users, nullable)
- `title` (text, required)
- `description` (text, nullable)
- `status` (ticket_status enum, default: 'open')
- `priority` (ticket_priority enum, default: 'medium')
- `created_at` (timestamptz)
- `updated_at` (timestamptz)
- `updated_by` (uuid, foreign key to auth.users)

#### `ticket_targets`
- `id` (integer, primary key)
- `ticket_id` (integer, foreign key to tickets)
- `target_type` (text, check: 'job', 'task', 'dataset')
- `target_id` (integer)

#### `ticket_messages`
- `id` (integer, primary key)
- `ticket_id` (integer, foreign key to tickets)
- `author_id` (uuid, foreign key to auth.users)
- `body` (text, required)
- `created_at` (timestamptz)

#### `ticket_status_history`
- `id` (integer, primary key)
- `ticket_id` (integer, foreign key to tickets)
- `old_status` (ticket_status enum, nullable)
- `new_status` (ticket_status enum)
- `changed_by` (uuid, foreign key to auth.users)
- `changed_at` (timestamptz)

### Enums

#### `ticket_status`
- `open`: Newly created tickets
- `in_progress`: Tickets being actively worked on
- `waiting_on_customer`: Waiting for customer response
- `resolved`: Issue has been resolved
- `closed`: Ticket is closed
- `cancelled`: Ticket was cancelled

#### `ticket_priority`
- `low`: Low priority issues
- `medium`: Standard priority (default)
- `high`: High priority issues
- `urgent`: Critical issues requiring immediate attention

## API Endpoints

### Core Ticket Operations

#### `GET /api/tickets`
List tickets with optional filtering
- Query parameters: `status`, `myTickets`, `page`, `limit`
- Returns: Array of tickets with creator, assignee, and targets

#### `POST /api/tickets`
Create a new ticket
- Body: `{ title, description?, priority?, assigneeId?, targets? }`
- Returns: Created ticket data

#### `GET /api/tickets/[id]`
Get a specific ticket with full details
- Returns: Ticket with comments and status history

#### `PUT /api/tickets/[id]`
Update ticket details
- Body: `{ title?, description?, priority?, assigneeId? }`
- Returns: Updated ticket data

#### `PATCH /api/tickets/[id]/status`
Update ticket status
- Body: `{ status }`
- Returns: Updated ticket data
- Automatically creates status history entry

### Comment Operations

#### `GET /api/tickets/[id]/comments`
Get comments for a ticket
- Query parameters: `page`, `limit`
- Returns: Array of comments with author information

#### `POST /api/tickets/[id]/comments`
Add a comment to a ticket
- Body: `{ body }`
- Returns: Created comment data

## React Components

### Core Components

#### `TicketList`
Displays a paginated, sortable table of tickets
- Props: `tickets`, `loading`, `onTicketClick`, `onStatusFilter`, `onSearch`
- Features: Search, status filtering, sorting, pagination

#### `TicketDetailDrawer`
Side drawer showing full ticket details
- Props: `open`, `ticket`, `onClose`, `onStatusChange`, `onAddComment`
- Features: Status updates, comment thread, ticket metadata

#### `CreateTicketDialog`
Modal dialog for creating new tickets
- Props: `open`, `onClose`, `onSubmit`, `preselectedTargets`
- Features: Form validation, target pre-selection, priority setting

#### `MyTicketsWidget`
Dashboard widget showing ticket summary
- Props: `tickets`, `loading`, `error`, `onTicketClick`
- Features: Status counts, recent tickets list

### Integration Components

#### Updated `SupportDialog`
Enhanced to include ticket creation option
- New "Create Ticket" button alongside traditional support request
- Automatically links tickets to current job/task/dataset context

## React Query Hooks

### Data Fetching Hooks

#### `useTickets(filters)`
Fetch tickets with filtering and real-time updates
- Parameters: `{ status?, myTickets?, page?, limit? }`
- Returns: Query result with tickets array

#### `useTicket(id)`
Fetch single ticket with real-time subscriptions
- Parameters: Ticket ID
- Returns: Query result with full ticket details
- Features: Automatic updates via Supabase realtime

#### `useTicketComments(ticketId, page?, limit?)`
Fetch comments for a specific ticket
- Returns: Query result with comments array

### Mutation Hooks

#### `useCreateTicket()`
Create new tickets with optimistic updates
- Returns: Mutation function and state

#### `useUpdateTicket()`
Update ticket details with optimistic updates
- Returns: Mutation function and state

#### `useUpdateTicketStatus()`
Update ticket status with optimistic updates
- Returns: Mutation function and state

#### `useAddComment()`
Add comments with optimistic updates
- Returns: Mutation function and state

## Real-time Features

The ticketing system uses Supabase real-time subscriptions to provide live updates:

- **Ticket Updates**: Changes to ticket status, priority, or details
- **New Comments**: Real-time comment notifications
- **Status History**: Live status change tracking

Subscriptions are automatically managed by the `useTicket` hook and clean up when components unmount.

## Access Control

### Row Level Security (RLS)

#### Tickets
- Users can access tickets where they are creator or assignee
- Admins have full access to all tickets

#### Comments
- Users can read/write comments on tickets they have access to
- Comment authors are automatically set to current user

#### Targets
- Access controlled through parent ticket permissions
- Read-only after creation

#### Status History
- Read-only for all users with ticket access
- Automatically populated by database triggers

## Usage Examples

### Creating a Ticket from Job Context

```typescript
const createTicket = useCreateTicket();

const handleCreateTicket = async () => {
  await createTicket.mutateAsync({
    title: "Job processing issue",
    description: "Job failed with error XYZ",
    priority: "high",
    targets: [{
      target_type: "job",
      target_id: jobId,
      name: jobName
    }]
  });
};
```

### Updating Ticket Status

```typescript
const updateStatus = useUpdateTicketStatus();

const handleStatusChange = async (ticketId: string, newStatus: string) => {
  await updateStatus.mutateAsync({
    id: ticketId,
    status: newStatus
  });
};
```

### Adding Comments

```typescript
const addComment = useAddComment();

const handleAddComment = async (ticketId: string, comment: string) => {
  await addComment.mutateAsync({
    ticketId,
    body: comment
  });
};
```

## Migration and Deployment

1. **Database Migration**: Run the migration file to create tables and triggers
2. **API Deployment**: Deploy new API routes
3. **Frontend Deployment**: Deploy updated components and hooks
4. **Testing**: Verify all functionality works correctly

## Future Enhancements

- Email notifications for ticket updates
- File attachments to tickets and comments
- Ticket templates for common issues
- Advanced search and filtering
- Ticket analytics and reporting
- Integration with external support systems
