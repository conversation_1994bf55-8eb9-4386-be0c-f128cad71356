-- Rename table
ALTER TABLE public.ticket_comments RENAME TO ticket_messages;

-- Rename sequence
ALTER SEQUENCE public.ticket_comments_id_seq RENAME TO ticket_messages_id_seq;

-- Rename indexes (primary key index rename automatically renames the constraint)
ALTER INDEX ticket_comments_pkey RENAME TO ticket_messages_pkey;
ALTER INDEX idx_ticket_comments_ticket_id RENAME TO idx_ticket_messages_ticket_id;

-- Rename foreign key constraints
ALTER TABLE public.ticket_messages RENAME CONSTRAINT ticket_comments_ticket_id_fkey TO ticket_messages_ticket_id_fkey;
ALTER TABLE public.ticket_messages RENAME CONSTRAINT ticket_comments_author_id_fkey TO ticket_messages_author_id_fkey;

-- Rename policies
ALTER POLICY ticket_comments_read ON public.ticket_messages RENAME TO ticket_messages_read;
ALTER POLICY ticket_comments_insert ON public.ticket_messages RENAME TO ticket_messages_insert;