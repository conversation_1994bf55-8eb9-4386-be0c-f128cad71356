import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";

export const PATCH = withAuth(async (userId, request, { params }) => {
    const supabase = createClient();
    const { id } = params;
    const { status } = await request.json();

    if (!id) {
        return NextResponse.json({ error: 'Ticket ID is required' }, { status: 400 });
    }

    // Validate status
    const validStatuses = ['open', 'in_progress', 'waiting_on_customer', 'resolved', 'closed', 'cancelled'];
    if (!status || !validStatuses.includes(status)) {
        return NextResponse.json({ error: 'Invalid status value' }, { status: 400 });
    }

    try {
        // Update the ticket status - the trigger will automatically create status history
        const { data: updatedTicket, error } = await supabase
            .from('tickets')
            .update({
                status,
                updated_by: userId
            })
            .eq('id', id)
            .select('*')
            .single();

        if (error) {
            if (error.code === 'PGRST116') {
                return NextResponse.json({ error: 'Ticket not found or access denied' }, { status: 404 });
            }
            return NextResponse.json({ error: error.message }, { status: 500 });
        }

        // Fetch user information separately
        const { data: users, error: usersError } = await supabase.auth.admin.listUsers();

        if (!usersError && users) {
            const userMap = new Map();
            users.users.forEach(user => {
                userMap.set(user.id, { id: user.id, email: user.email });
            });

            // Add user information to ticket
            updatedTicket.creator = userMap.get(updatedTicket.creator_id) || null;
            updatedTicket.assignee = userMap.get(updatedTicket.assignee_id) || null;
        }

        return NextResponse.json({ success: true, data: updatedTicket });
    } catch (error) {
        console.error('Ticket status update error:', error);
        return NextResponse.json({ error: 'Failed to update ticket status' }, { status: 500 });
    }
});

export const GET = withAuth(async (userId, request, { params }) => {
    const supabase = createClient();
    const { id } = params;

    if (!id) {
        return NextResponse.json({ error: 'Ticket ID is required' }, { status: 400 });
    }

    try {
        const { data, error } = await supabase
            .from('tickets')
            .select('id, status, updated_at')
            .eq('id', id)
            .single();

        if (error) {
            if (error.code === 'PGRST116') {
                return NextResponse.json({ error: 'Ticket not found or access denied' }, { status: 404 });
            }
            return NextResponse.json({ error: error.message }, { status: 500 });
        }

        return NextResponse.json({ success: true, data });
    } catch (error) {
        console.error('Ticket status fetch error:', error);
        return NextResponse.json({ error: 'Failed to fetch ticket status' }, { status: 500 });
    }
});
